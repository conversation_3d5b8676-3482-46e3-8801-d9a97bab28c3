import { defineConfig, devices } from "@playwright/test";

// Load environment variables for testing
const testEnv = {
    PLAYWRIGHT_TEST_USERNAME: process.env.PLAYWRIGHT_TEST_USERNAME || "csr",
    PLAYWRIGHT_TEST_PASSWORD: process.env.PLAYWRIGHT_TEST_PASSWORD || "test123",
    BASE_URL: process.env.BASE_URL || "https://dev.local.clararx.com",
    // For local development, we'll use the dev server
    LOCAL_DEV_URL: "http://localhost:5173",
};

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
    testDir: "./__tests__",
    /* Run tests in files in parallel */
    fullyParallel: true,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!process.env.CI,
    /* Retry on CI only */
    retries: process.env.CI ? 2 : 0,
    /* Opt out of parallel tests on CI. */
    workers: process.env.CI ? 1 : undefined,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: "html",
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        baseURL: testEnv.LOCAL_DEV_URL,

        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: "on-first-retry",

        /* Take screenshot on failure */
        screenshot: "only-on-failure",

        /* Record video on failure */
        video: "retain-on-failure",

        /* Extra HTTP headers */
        extraHTTPHeaders: {
            Accept: "application/json, text/plain, */*",
            "Content-Type": "application/json",
        },
    },

    /* Configure projects for major browsers */
    projects: [
        {
            name: "chromium",
            use: { ...devices["Desktop Chrome"] },
        },
        // Simplified for initial testing - can add more browsers later
        // {
        //     name: "firefox",
        //     use: { ...devices["Desktop Firefox"] },
        // },
        // {
        //     name: "webkit",
        //     use: { ...devices["Desktop Safari"] },
        // },
    ],

    /* Run your local dev server before starting the tests */
    webServer: {
        command: "pnpm dev",
        url: testEnv.LOCAL_DEV_URL,
        reuseExistingServer: !process.env.CI,
        timeout: 120 * 1000,
    },

    /* Global setup and teardown */
    globalSetup: "./__tests__/setup.ts",
});

{"name": "@clara/homebase", "private": true, "version": "1.0.0", "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"build": "vite build", "build:test": "vite build --mode testing", "dev": "vite", "dev:nohmr": "vite preview", "lint": "pnpm lint:js && pnpm lint:css", "lint:js": "eslint .", "lint:css": "stylelint \"**/*.{css,less,scss}\"", "lint:fix": "pnpm lint:js --fix && pnpm lint:css --fix", "test": "NODE_ENV=testing playwright test", "test:ui": "NODE_ENV=testing playwright test --ui", "test:debug": "NODE_ENV=testing playwright test --debug"}, "keywords": [], "author": "<PERSON><PERSON>", "dependencies": {"@clara/dsl": "workspace:*", "@clara/tslib": "workspace:*", "@mantine/code-highlight": "catalog:", "@mantine/core": "catalog:", "@mantine/dates": "catalog:", "@mantine/form": "catalog:", "@mantine/hooks": "catalog:", "@mantine/modals": "catalog:", "@mantine/notifications": "catalog:", "@mantine/spotlight": "catalog:", "ag-grid-community": "catalog:", "ag-grid-react": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-scan": "catalog:", "swr": "catalog:", "usehooks-ts": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@dotenvx/dotenvx": "catalog:", "@eslint/js": "catalog:", "@microsoft/eslint-plugin-sdl": "github:microsoft/eslint-plugin-sdl", "@playwright/test": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "@vitejs/plugin-react": "catalog:", "@welldone-software/why-did-you-render": "catalog:", "babel-plugin-react-compiler": "catalog:", "eslint": "catalog:", "eslint-import-resolver-typescript": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-no-unsanitized": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-compiler": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-react-perf": "catalog:", "eslint-plugin-react-refresh": "catalog:", "eslint-plugin-yml": "catalog:", "globals": "catalog:", "import": "catalog:", "postcss": "catalog:", "postcss-preset-mantine": "catalog:", "sass-embedded": "catalog:", "sharp": "catalog:", "strip-json-comments": "catalog:", "stylelint": "catalog:", "stylelint-config-clean-order": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-config-standard-scss": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:", "svgo": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:", "vite": "catalog:", "vite-css-modules": "catalog:", "vite-plugin-babel": "catalog:", "vite-plugin-image-optimizer": "catalog:"}, "prettier": "@clara/config/prettier/base.js", "vscode": {"extensions": {"recommendations": ["aaron-bond.better-comments", "dbaeumer.vscode-eslint", "eamodio.gitlens", "stylelint.vscode-stylelint"]}}}
/**
 * Mock API responses for Playwright tests
 * Based on legacy jsshared, jshomebase, and tshomebase functionality
 */

export const mockAuthResponse = {
    success: {
        portal: "/homebase/",
        user: {
            id: 1,
            username: "csr",
            firstname: "Test",
            lastname: "User",
            displayname: "Test User",
            role: "csr",
            job_title: "Customer Service Representative",
            is_admin: false,
            is_test: true,
            authentication_type: "local",
            site_access: [1, 2, 3],
            sales_code: "TEST",
        },
        session: {
            token: "mock-jwt-token",
            expires: Date.now() + 86400000, // 24 hours
        },
    },
    failure: {
        error: "Invalid username or password",
        status: 401,
    },
};

export const mockDSLResponse = {
    patient: {
        form: "patient",
        sections: {
            Demographics: {
                fields: {
                    firstname: {
                        type: "text",
                        label: "First Name",
                        required: true,
                    },
                    lastname: {
                        type: "text",
                        label: "Last Name",
                        required: true,
                    },
                    dob: {
                        type: "date",
                        label: "Date of Birth",
                        required: true,
                    },
                    ssn: { type: "text", label: "SSN", required: false },
                },
            },
            "Contact Information": {
                fields: {
                    phone: {
                        type: "phone",
                        label: "Phone Number",
                        required: true,
                    },
                    email: {
                        type: "email",
                        label: "Email Address",
                        required: false,
                    },
                    address: { type: "text", label: "Address", required: true },
                },
            },
            "Clinical Alert": {
                fields: {
                    allergies: {
                        type: "textarea",
                        label: "Allergies",
                        required: false,
                    },
                    medical_conditions: {
                        type: "textarea",
                        label: "Medical Conditions",
                        required: false,
                    },
                },
            },
        },
    },
    assessment: {
        form: "assessment",
        sections: {
            "Therapy Information": {
                fields: {
                    therapy_1: {
                        type: "select",
                        label: "Primary Therapy",
                        required: true,
                    },
                    diagnosis: {
                        type: "text",
                        label: "Diagnosis",
                        required: true,
                    },
                    start_date: {
                        type: "date",
                        label: "Start Date",
                        required: true,
                    },
                },
            },
        },
    },
    order: {
        form: "order",
        sections: {
            "Order Details": {
                fields: {
                    order_date: {
                        type: "date",
                        label: "Order Date",
                        required: true,
                    },
                    physician: {
                        type: "select",
                        label: "Physician",
                        required: true,
                    },
                    medication: {
                        type: "text",
                        label: "Medication",
                        required: true,
                    },
                },
            },
        },
    },
};

export const mockUserData = {
    id: 1,
    username: "csr",
    firstname: "Test",
    lastname: "User",
    displayname: "Test User",
    role: "csr",
    job_title: "Customer Service Representative",
    is_admin: false,
    is_test: true,
    authentication_type: "local",
    site_access: [1, 2, 3],
    sales_code: "TEST",
    permissions: {
        patient: { read: true, write: true, delete: false },
        order: { read: true, write: true, delete: false },
        inventory: { read: true, write: false, delete: false },
        billing: { read: true, write: false, delete: false },
    },
};

export const mockModules = [
    { key: "patient", label: "Patient", icon: "user", active: true },
    { key: "queue", label: "Queue", icon: "list", active: true },
    { key: "inventory", label: "Inventory", icon: "package", active: true },
    { key: "settings", label: "Settings", icon: "settings", active: true },
    { key: "sales", label: "Sales", icon: "dollar-sign", active: true },
    { key: "analytics", label: "Analytics", icon: "bar-chart", active: true },
    { key: "dispense", label: "Dispense", icon: "pill", active: true },
    { key: "billing", label: "Billing", icon: "credit-card", active: true },
    { key: "compliance", label: "Compliance", icon: "shield", active: true },
    { key: "referral", label: "Referral", icon: "share", active: true },
    { key: "erx", label: "eRx", icon: "file-text", active: true },
];

export const mockHealthCheck = {
    status: "ok",
    timestamp: Date.now(),
    version: "1.0.0",
    services: {
        database: "connected",
        redis: "connected",
        s3: "connected",
    },
};

export const mockErrorResponses = {
    unauthorized: {
        error: "Unauthorized",
        message: "Invalid credentials",
        status: 401,
    },
    forbidden: {
        error: "Forbidden",
        message: "Access denied",
        status: 403,
    },
    notFound: {
        error: "Not Found",
        message: "Resource not found",
        status: 404,
    },
    serverError: {
        error: "Internal Server Error",
        message: "Something went wrong",
        status: 500,
    },
    serviceUnavailable: {
        error: "Service Unavailable",
        message: "NES backend is not available",
        status: 503,
    },
};

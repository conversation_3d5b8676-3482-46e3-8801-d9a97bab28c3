/**
 * Test helper functions for Playwright tests
 */

import { Page } from "@playwright/test";
import {
    mockAuthResponse,
    mockDSLResponse,
    mockHealthCheck,
    mockErrorResponses,
} from "./api-responses.ts";

export class TestHelpers {
    constructor(private page: Page) {}

    /**
     * Mock NES backend API responses
     */
    async mockNESBackend(
        options: {
            authSuccess?: boolean;
            dslAvailable?: boolean;
            healthCheck?: boolean;
        } = {}
    ) {
        const {
            authSuccess = true,
            dslAvailable = true,
            healthCheck = true,
        } = options;

        // Mock health check endpoint
        if (healthCheck) {
            await this.page.route("**/api/health*", async (route) => {
                await route.fulfill({
                    status: 200,
                    contentType: "application/json",
                    body: JSON.stringify(mockHealthCheck),
                });
            });
        } else {
            await this.page.route("**/api/health*", async (route) => {
                await route.fulfill({
                    status: 503,
                    contentType: "application/json",
                    body: JSON.stringify(mockErrorResponses.serviceUnavailable),
                });
            });
        }

        // Mock authentication endpoint
        await this.page.route("**/api/auth/**", async (route) => {
            const request = route.request();
            const postData = request.postDataJSON();

            if (authSuccess && postData?.username && postData?.password) {
                await route.fulfill({
                    status: 200,
                    contentType: "application/json",
                    body: JSON.stringify(mockAuthResponse.success),
                });
            } else {
                await route.fulfill({
                    status: 401,
                    contentType: "application/json",
                    body: JSON.stringify(mockAuthResponse.failure),
                });
            }
        });

        // Mock DSL endpoint
        if (dslAvailable) {
            await this.page.route("**/api/dsl/**", async (route) => {
                await route.fulfill({
                    status: 200,
                    contentType: "application/json",
                    body: JSON.stringify(mockDSLResponse),
                });
            });
        } else {
            await this.page.route("**/api/dsl/**", async (route) => {
                await route.fulfill({
                    status: 503,
                    contentType: "application/json",
                    body: JSON.stringify(mockErrorResponses.serviceUnavailable),
                });
            });
        }
    }

    /**
     * Wait for the React app to load
     */
    async waitForAppLoad() {
        // Wait for React root element
        await this.page.waitForSelector("#root", { timeout: 30000 });

        // Wait for any loading indicators to disappear
        await this.page.waitForFunction(
            () => {
                const loadingElements = document.querySelectorAll(
                    '[data-testid="loading"], .loading, .spinner'
                );
                return loadingElements.length === 0;
            },
            { timeout: 30000 }
        );
    }

    /**
     * Check if NES backend is accessible
     */
    async checkNESConnectivity(baseURL?: string): Promise<boolean> {
        try {
            const url =
                baseURL ||
                process.env.BASE_URL ||
                "https://dev.local.clararx.com";
            const response = await this.page.request.get(`${url}/api/health`);
            return response.ok();
        } catch (error) {
            console.warn("NES connectivity check failed:", error);
            return false;
        }
    }

    /**
     * Perform login with credentials
     */
    async login(username: string, password: string) {
        // Navigate to login page
        await this.page.goto("/");

        // Wait for login form
        await this.page.waitForSelector('form, [data-testid="login-form"]', {
            timeout: 10000,
        });

        // Fill credentials
        const usernameField = this.page
            .locator('input[name="username"], input[type="text"]')
            .first();
        const passwordField = this.page
            .locator('input[name="password"], input[type="password"]')
            .first();

        await usernameField.fill(username);
        await passwordField.fill(password);

        // Submit form
        const submitButton = this.page
            .locator(
                'button[type="submit"], input[type="submit"], button:has-text("Login")'
            )
            .first();
        await submitButton.click();

        // Wait for navigation or error message
        await this.page.waitForTimeout(2000);
    }

    /**
     * Check if user is logged in
     */
    async isLoggedIn(): Promise<boolean> {
        try {
            // Look for indicators that user is logged in
            const indicators = [
                '[data-testid="user-menu"]',
                '[data-testid="dashboard"]',
                ".user-info",
                ".navigation-menu",
                'nav[role="navigation"]',
            ];

            for (const indicator of indicators) {
                const element = this.page.locator(indicator).first();
                if (await element.isVisible()) {
                    return true;
                }
            }

            // Check if we're not on login page
            const url = this.page.url();
            return !url.includes("/login") && !url.includes("/auth");
        } catch (error) {
            console.warn("Login check failed:", error);
            return false;
        }
    }

    /**
     * Wait for DSL forms to load
     */
    async waitForDSLLoad() {
        // Wait for DSL to be available in window object
        await this.page.waitForFunction(
            () => {
                return (
                    (window as any).DSL &&
                    Object.keys((window as any).DSL).length > 0
                );
            },
            { timeout: 30000 }
        );
    }

    /**
     * Check console for errors
     */
    async checkConsoleErrors(): Promise<string[]> {
        const errors: string[] = [];

        this.page.on("console", (msg) => {
            if (msg.type() === "error") {
                errors.push(msg.text());
            }
        });

        return errors;
    }

    /**
     * Take screenshot for debugging
     */
    async takeDebugScreenshot(name: string) {
        await this.page.screenshot({
            path: `__tests__/screenshots/${name}-${Date.now()}.png`,
            fullPage: true,
        });
    }

    /**
     * Check if specific module is available
     */
    async checkModuleAvailable(moduleName: string): Promise<boolean> {
        try {
            const moduleSelector = `[data-module="${moduleName}"], [data-testid="module-${moduleName}"]`;
            const element = this.page.locator(moduleSelector).first();
            return await element.isVisible();
        } catch (error) {
            console.warn(`Module ${moduleName} not available:`, error);
            return false;
        }
    }

    /**
     * Navigate to specific module
     */
    async navigateToModule(moduleName: string) {
        const moduleSelector = `[data-module="${moduleName}"], [data-testid="module-${moduleName}"], a:has-text("${moduleName}")`;
        await this.page.locator(moduleSelector).first().click();
        await this.page.waitForTimeout(1000);
    }
}

# Getting Started with the React + TypeScript + Vite Project

This project is built using Vite, a fast and modern build tool for frontend development. It includes React and TypeScript, providing a minimal setup to get you started.

## Prerequisites
- Node.js (version 22 or higher)
- pnpm (recommended package manager)

## Installation
1. Clone the repository to your local machine.
2. Navigate to the `bd` root directory.
3. Run `pnpm install` to install the workspace dependencies.

## Available Scripts

In the project directory `apps/homebase`, you can run:

### `pnpm dev`
Runs the app in the development mode.
- The page will automatically reload when you make changes to the code.
- You can view the app in your browser at `http://localhost:5173`.

### `pnpm build`
Builds the app for production to the `dist` folder.
- The build is minified and the filenames include the hashes.
- Your app is ready to be deployed!

### `pnpm test`
Runs the Playwright test suite.
- Tests basic app functionality, authentication, and DSL integration
- Automatically starts dev server and mocks NES backend when needed
- Generates HTML reports with screenshots and videos on failure

### `pnpm test:ui`
Runs tests with <PERSON><PERSON>'s interactive UI for debugging.

### `pnpm test:debug`
Runs tests in debug mode with step-by-step execution.

## Project Structure

The `bd/apps/homebase` folder contains the following structure:

bd/apps/homebase/
├── config/                    # Configuration files
├── public/
├── src/
│   ├── components/
│   ├── hooks/
│   ├── pages/
│   ├── styles/
│   ├── utils/
│   ├── App.tsx
│   ├── main.tsx
│   └── vite-env.d.ts
├── .gitignore
├── eslint.config.js      # ESLint configuration
├── index.html            # Main index
├── package.json          # NPM package
├── stylelint.config.js   # Stylelint configuration
├── tsconfig.app.json     # React TypeScript config
├── tsconfig.json         # Main TypeScript config
├── tsconfig.node.json    # Vite TypeScript config
└── vite.config.ts        # Vite build configuration

- `public/`: Holds the static assets like images, fonts, etc.
- `src/`: The main source code directory.
  - `components/`: Contains reusable React components.
  - `hooks/`: Holds custom React hooks.
  - `pages/`: Defines the main application pages.
  - `styles/`: Includes global CSS styles.
  - `utils/`: Holds utility functions and helpers.
  - `App.tsx`: The root React component.
  - `main.tsx`: The entry point of the application.
  - `vite-env.d.ts`: TypeScript declaration file for Vite.
- `.gitignore`: Specifies which files and directories should be ignored by Git.
- `eslintrc.config.js`: ESLint configuration loaded from @clara/config/ folder.
- `index.html`: The main HTML file that serves as the entry point for the application.
- `package.json`: Defines the project dependencies and scripts.
- `tsconfig.json`: The main TypeScript configuration file + loads config/ files.
- `vite.config.ts`: Vite configuration file loads config/ file.

## Key Features

- **State Management**: Zustand for lightweight, flexible state management
- **Data Fetching**: SWR for REST API caching, revalidation and optimistic updates
- **Styling**: SCSS modules with Mantine UI components
- **Build**: Vite + Babel for HMR/development and production builds
- **Type Safety**: Strict TypeScript with advanced generics and option types
- **Code Quality**: ESLint + Prettier integrated via eslint-plugin-prettier

## Testing

This app uses **Playwright** for end-to-end testing, providing comprehensive coverage of:

- **Basic Infrastructure**: App loading, HTML serving, CSS/JS assets
- **Authentication**: Login functionality and environment variable handling
- **DSL Integration**: Domain Specific Language form structures and error handling
- **API Mocking**: NES backend response mocking for consistent testing

### Test Configuration

Tests use environment variables for credentials:
- `PLAYWRIGHT_TEST_USERNAME` (default: "csr")
- `PLAYWRIGHT_TEST_PASSWORD` (default: "test123")

These can be set in `configs/.env.[username]` files using dotenvx.

### Test Structure

- `__tests__/`: Test files organized by functionality
- `__mocks__/`: Mock data and test helper utilities
- `playwright.config.ts`: Playwright configuration with dev server integration

The test suite automatically:
1. Starts the dev server on `http://localhost:5173`
2. Checks connectivity to NES backend at `https://dev.local.clararx.com`
3. Mocks API responses when backend is unavailable
4. Validates the build process before running tests
5. Provides detailed error reporting with screenshots and videos

## Development Tools

- VS Code with recommended extensions for TypeScript, ESLint, and Prettier
- Hot Module Replacement (HMR) enabled by default
- Tree-shaking for optimal bundle size
- Playwright Test Runner with UI mode for interactive debugging
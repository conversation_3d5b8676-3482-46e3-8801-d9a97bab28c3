/**
 * Basic infrastructure tests for apps/homebase
 * Tests basic server connectivity and HTML loading
 */

import { test, expect } from "@playwright/test";
import { TestHelpers } from "../__mocks__/test-helpers";

test.describe("Basic Infrastructure", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);
        // Mock all API endpoints to prevent network calls
        await helpers.mockNESBackend({
            healthCheck: true,
            dslAvailable: true,
            authSuccess: true,
        });
    });

    test("should serve the HTML page", async ({ page }) => {
        const response = await page.goto("/");

        // Check that the page loads successfully
        expect(response?.status()).toBe(200);

        // Check that we have the basic HTML structure
        const rootElement = page.locator("#root");
        await expect(rootElement).toBeAttached();
    });

    test("should load basic CSS and JS assets", async ({ page }) => {
        await page.goto("/");

        // Check that the page has loaded some content
        const bodyContent = await page.textContent("body");
        expect(bodyContent).toBeDefined();

        // Check that we have some basic elements
        const rootElement = page.locator("#root");
        await expect(rootElement).toBeAttached();
    });
});

test.describe("API Mocking", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);
    });

    test("should intercept API calls", async ({ page }) => {
        // Set up API mocking
        await helpers.mockNESBackend({
            healthCheck: true,
            dslAvailable: true,
            authSuccess: true,
        });

        // Track network requests
        const requests: string[] = [];
        page.on("request", (request) => {
            requests.push(request.url());
        });

        // Navigate to the app
        const response = await page.goto("/");
        expect(response?.status()).toBe(200);

        // Wait a bit for any async requests
        await page.waitForTimeout(2000);

        // Check that some requests were made (even if mocked)
        expect(requests.length).toBeGreaterThan(0);
    });
});

/**
 * DSL (Domain Specific Language) tests for apps/homebase
 * Tests basic DSL API functionality
 */

import { test, expect } from "@playwright/test";
import { TestHelpers } from "../__mocks__/test-helpers.ts";

test.describe("DSL API", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);

        // Mock NES backend with DSL support
        await helpers.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });
    });

    test("should have mock DSL data available", async ({ page }) => {
        // Test that our mock DSL data structure is correct
        const mockDSL = await page.evaluate(() => {
            // Import the mock data directly
            return {
                patient: {
                    form: "patient",
                    sections: {
                        Demographics: {
                            fields: {
                                firstname: { type: "text", label: "First Name" }
                            }
                        }
                    }
                }
            };
        });

        expect(mockDSL).toHaveProperty("patient");
        expect(mockDSL.patient).toHaveProperty("form", "patient");
        expect(mockDSL.patient).toHaveProperty("sections");
    });

    test("should have patient form structure in mock data", async ({ page }) => {
        // Test the structure we expect from DSL
        const mockPatient = await page.evaluate(() => {
            return {
                sections: {
                    Demographics: {
                        fields: {
                            firstname: { type: "text", label: "First Name" },
                            lastname: { type: "text", label: "Last Name" }
                        }
                    }
                }
            };
        });

        expect(mockPatient.sections).toHaveProperty("Demographics");
        expect(mockPatient.sections.Demographics).toHaveProperty("fields");
        expect(mockPatient.sections.Demographics.fields).toHaveProperty("firstname");
    });

    test("should have assessment form structure in mock data", async ({ page }) => {
        // Test the assessment structure we expect
        const mockAssessment = await page.evaluate(() => {
            return {
                form: "assessment",
                sections: {
                    "Clinical Assessment": {
                        fields: {
                            therapy_1: { type: "select", label: "Therapy 1" }
                        }
                    }
                }
            };
        });

        expect(mockAssessment).toHaveProperty("form", "assessment");
        expect(mockAssessment).toHaveProperty("sections");
        expect(mockAssessment.sections).toHaveProperty("Clinical Assessment");
    });

});

test.describe("DSL Error Handling", () => {

    test("should handle DSL loading failures", async ({ page }) => {
        // Test that we can handle when DSL is not available
        const errorHandling = await page.evaluate(() => {
            try {
                // Simulate DSL not being available
                const dslAvailable = false;

                if (!dslAvailable) {
                    return { success: true, handled: true, error: "DSL not available" };
                }

                return { success: true, handled: false };
            } catch (error) {
                return { success: false, error: String(error) };
            }
        });

        expect(errorHandling.success).toBeTruthy();
        expect(errorHandling.handled).toBeTruthy();
    });

    test("should handle malformed DSL data", async ({ page }) => {
        // Test that we can handle malformed DSL data
        const errorHandling = await page.evaluate(() => {
            try {
                const malformedDSL = { invalid: "response" };

                // Check if this is valid DSL structure
                const isValidDSL = malformedDSL.hasOwnProperty('patient') ||
                                  malformedDSL.hasOwnProperty('assessment');

                return {
                    success: true,
                    isValid: isValidDSL,
                    handled: !isValidDSL
                };
            } catch (error) {
                return { success: false, error: String(error) };
            }
        });

        expect(errorHandling.success).toBeTruthy();
        expect(errorHandling.isValid).toBeFalsy();
        expect(errorHandling.handled).toBeTruthy();
    });
});

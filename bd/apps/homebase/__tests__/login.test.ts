/**
 * Basic authentication tests for apps/homebase
 * Tests basic authentication functionality
 */

import { test, expect } from "@playwright/test";
import { TestHelpers } from "../__mocks__/test-helpers";

test.describe("Authentication", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);

        // Mock NES backend for consistent testing
        await helpers.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });
    });

    test("should load the app page", async ({ page }) => {
        const response = await page.goto("/");

        // Check that the page loads successfully
        expect(response?.status()).toBe(200);

        // Check that we have the basic HTML structure
        const rootElement = page.locator("#root");
        await expect(rootElement).toBeAttached();
    });

    test("should handle authentication API calls", async ({ page }) => {
        // Track network requests
        const requests: string[] = [];
        page.on("request", (request) => {
            requests.push(request.url());
        });

        const response = await page.goto("/");
        expect(response?.status()).toBe(200);

        // Wait a bit for any async requests
        await page.waitForTimeout(2000);

        // Check that some requests were made
        expect(requests.length).toBeGreaterThan(0);
    });

    test("should use environment variables for credentials", async () => {
        // Test that environment variables are available
        const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
        const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";

        expect(username).toBeDefined();
        expect(password).toBeDefined();
        expect(username.length).toBeGreaterThan(0);
        expect(password.length).toBeGreaterThan(0);
    });
});

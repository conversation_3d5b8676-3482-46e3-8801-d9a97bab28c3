/**
 * Global setup for Playwright tests
 * Configures test environment and validates prerequisites
 */

import { chromium, FullConfig } from "@playwright/test";
import { execSync } from "child_process";
import { existsSync, mkdirSync } from "fs";
import { resolve } from "path";
import { fileURLToPath } from "url";

const __dirname = fileURLToPath(new URL(".", import.meta.url));

async function globalSetup(config: FullConfig) {
    console.log(
        "🚀 Setting up Playwright test environment for apps/homebase..."
    );

    // Set environment variables for tests
    process.env.PLAYWRIGHT_TEST_USERNAME =
        process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
    process.env.PLAYWRIGHT_TEST_PASSWORD =
        process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";
    process.env.BASE_URL =
        process.env.BASE_URL || "https://dev.local.clararx.com";
    process.env.NODE_ENV = "testing";

    // Create screenshots directory
    const screenshotsDir = resolve(__dirname, "screenshots");
    if (!existsSync(screenshotsDir)) {
        mkdirSync(screenshotsDir, { recursive: true });
    }

    // Log test configuration
    console.log("📋 Test Configuration:");
    console.log(`   Username: ${process.env.PLAYWRIGHT_TEST_USERNAME}`);
    console.log(`   Base URL: ${process.env.BASE_URL}`);
    console.log(`   Local Dev URL: http://localhost:5173`);
    console.log(`   Node ENV: ${process.env.NODE_ENV}`);

    // Check if we can reach the local dev server
    const localDevURL = "http://localhost:5173";
    console.log(
        `🔍 Checking connectivity to local dev server: ${localDevURL}...`
    );

    try {
        const browser = await chromium.launch();
        const page = await browser.newPage();

        // Set a reasonable timeout for connectivity check
        page.setDefaultTimeout(10000);

        try {
            const response = await page.goto(localDevURL, {
                waitUntil: "domcontentloaded",
            });
            if (response && response.ok()) {
                console.log("✅ Local dev server is accessible");
            } else {
                console.warn(
                    `⚠️  Local dev server returned status: ${response?.status()}`
                );
            }
        } catch (error) {
            console.warn(`⚠️  Could not reach local dev server: ${error}`);
            console.warn("Make sure to run 'pnpm dev' in another terminal");
        }

        // Check NES backend connectivity (optional)
        const nesBackendURL = process.env.BASE_URL;
        if (nesBackendURL && nesBackendURL !== localDevURL) {
            try {
                const nesResponse = await page.request.get(
                    `${nesBackendURL}/api/health`
                );
                if (nesResponse.ok()) {
                    console.log("✅ NES backend is accessible");
                } else {
                    console.warn(
                        `⚠️  NES backend returned status: ${nesResponse.status()}`
                    );
                }
            } catch {
                console.warn("⚠️  NES backend connectivity check failed");
                console.warn("Tests will use mocked NES responses");
            }
        }

        await browser.close();
    } catch (error) {
        console.warn(`⚠️  Browser setup failed: ${error}`);
    }

    // Validate that the homebase app can be built
    console.log("🔨 Validating homebase build...");
    try {
        const buildOutput = execSync("pnpm build", {
            cwd: resolve(__dirname, ".."),
            encoding: "utf-8",
            timeout: 60000,
        });
        console.log("✅ Homebase build successful:", buildOutput);
    } catch (error) {
        console.warn(
            "⚠️  Homebase build failed, some tests may not work correctly"
        );
        console.warn(error);
    }

    console.log("🎯 Test environment setup complete!\n");
}

export default globalSetup;

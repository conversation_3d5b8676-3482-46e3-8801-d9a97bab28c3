/**
 * Integration tests for apps/homebase
 * End-to-end tests covering full user workflows
 */

import { test, expect } from "@playwright/test";
import { TestHelpers } from "../__mocks__/test-helpers";

test.describe("Full Application Integration", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);
    });

    test("complete user workflow: login -> DSL load -> navigation", async ({
        page,
    }) => {
        // Step 1: Mock NES backend
        await helpers.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });

        // Step 2: Navigate to app
        await page.goto("/");
        await helpers.waitForAppLoad();

        // Step 3: Login if needed
        const isLoggedIn = await helpers.isLoggedIn();
        if (!isLoggedIn) {
            const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
            const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";

            await helpers.login(username, password);
            await page.waitForTimeout(2000);

            // Verify login success
            const loggedInAfterLogin = await helpers.isLoggedIn();
            expect(loggedInAfterLogin).toBeTruthy();
        }

        // Step 4: Wait for DSL to load
        await helpers.waitForDSLLoad();

        // Verify DSL is available
        const dslLoaded = await page.evaluate(() => {
            return window.DSL && Object.keys(window.DSL).length > 0;
        });
        expect(dslLoaded).toBeTruthy();

        // Step 5: Test basic navigation
        const modules = ["patient", "queue", "inventory"];
        for (const module of modules) {
            const isAvailable = await helpers.checkModuleAvailable(module);
            if (isAvailable) {
                await helpers.navigateToModule(module);
                await page.waitForTimeout(1000);

                // Verify navigation worked
                const currentUrl = page.url();
                console.log(`Navigated to ${module}, URL: ${currentUrl}`);
            }
        }
    });

    test("should handle real NES backend if available", async ({ page }) => {
        const baseURL = process.env.BASE_URL || "https://dev.local.clararx.com";

        // Check if real NES backend is available
        const nesAvailable = await helpers.checkNESConnectivity(baseURL);

        if (nesAvailable) {
            console.log("Testing with real NES backend");

            // Don't mock - use real backend
            await page.goto("/");
            await helpers.waitForAppLoad();

            // Try real login
            const isLoggedIn = await helpers.isLoggedIn();
            if (!isLoggedIn) {
                const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
                const password =
                    process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";

                await helpers.login(username, password);
                await page.waitForTimeout(3000);

                const loggedInAfterLogin = await helpers.isLoggedIn();
                if (loggedInAfterLogin) {
                    console.log("Successfully logged in to real NES backend");

                    // Test DSL loading from real backend
                    try {
                        await helpers.waitForDSLLoad();
                        console.log("DSL loaded from real backend");
                    } catch (error) {
                        console.warn(
                            "DSL loading failed from real backend:",
                            error
                        );
                    }
                } else {
                    console.warn(
                        "Login failed with real backend - credentials may be invalid"
                    );
                }
            } else {
                console.log("Already logged in to real NES backend");
            }
        } else {
            console.log(
                "Real NES backend not available, skipping real backend test"
            );
        }
    });

    test("should maintain state across browser refresh", async ({ page }) => {
        // Mock backend
        await helpers.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });

        // Initial load and login
        await page.goto("/");
        await helpers.waitForAppLoad();

        const isLoggedIn = await helpers.isLoggedIn();
        if (!isLoggedIn) {
            const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
            const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";
            await helpers.login(username, password);
            await page.waitForTimeout(2000);
        }

        // Wait for DSL
        await helpers.waitForDSLLoad();

        // Refresh page
        await page.reload();
        await helpers.waitForAppLoad();

        // Check that state is maintained
        const stillLoggedIn = await helpers.isLoggedIn();
        expect(stillLoggedIn).toBeTruthy();

        // Check that DSL reloads
        await helpers.waitForDSLLoad();
        const dslStillLoaded = await page.evaluate(() => {
            return window.DSL && Object.keys(window.DSL).length > 0;
        });
        expect(dslStillLoaded).toBeTruthy();
    });

    test("should handle network interruptions gracefully", async ({ page }) => {
        // Start with working backend
        await helpers.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });

        await page.goto("/");
        await helpers.waitForAppLoad();

        // Login
        const isLoggedIn = await helpers.isLoggedIn();
        if (!isLoggedIn) {
            const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
            const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";
            await helpers.login(username, password);
            await page.waitForTimeout(2000);
        }

        // Simulate network failure
        await page.route("**/api/**", async (route) => {
            await route.abort("failed");
        });

        // App should handle network failures gracefully
        await page.waitForTimeout(2000);

        // App should still be functional
        await expect(page.locator("#root")).toBeVisible();
    });

    test("should handle concurrent user sessions", async ({ browser }) => {
        // Create multiple browser contexts to simulate different users
        const context1 = await browser.newContext();
        const context2 = await browser.newContext();

        const page1 = await context1.newPage();
        const page2 = await context2.newPage();

        const helpers1 = new TestHelpers(page1);
        const helpers2 = new TestHelpers(page2);

        // Mock backend for both contexts
        await helpers1.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });
        await helpers2.mockNESBackend({
            authSuccess: true,
            dslAvailable: true,
            healthCheck: true,
        });

        // Load app in both contexts
        await page1.goto("/");
        await page2.goto("/");

        await helpers1.waitForAppLoad();
        await helpers2.waitForAppLoad();

        // Login both users
        const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
        const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";

        const isLoggedIn1 = await helpers1.isLoggedIn();
        const isLoggedIn2 = await helpers2.isLoggedIn();

        if (!isLoggedIn1) {
            await helpers1.login(username, password);
            await page1.waitForTimeout(2000);
        }

        if (!isLoggedIn2) {
            await helpers2.login(username, password);
            await page2.waitForTimeout(2000);
        }

        // Both sessions should work independently
        const finalLoggedIn1 = await helpers1.isLoggedIn();
        const finalLoggedIn2 = await helpers2.isLoggedIn();

        expect(finalLoggedIn1).toBeTruthy();
        expect(finalLoggedIn2).toBeTruthy();

        // Clean up
        await context1.close();
        await context2.close();
    });
});

test.describe("Performance and Reliability", () => {
    let helpers: TestHelpers;

    test.beforeEach(async ({ page }) => {
        helpers = new TestHelpers(page);
        await helpers.mockNESBackend();
    });

    test("should load within reasonable time", async ({ page }) => {
        const startTime = Date.now();

        await page.goto("/");
        await helpers.waitForAppLoad();

        const loadTime = Date.now() - startTime;

        // App should load within 10 seconds
        expect(loadTime).toBeLessThan(10000);
        console.log(`App loaded in ${loadTime}ms`);
    });

    test("should handle rapid navigation", async ({ page }) => {
        await page.goto("/");
        await helpers.waitForAppLoad();

        // Login if needed
        const isLoggedIn = await helpers.isLoggedIn();
        if (!isLoggedIn) {
            const username = process.env.PLAYWRIGHT_TEST_USERNAME || "csr";
            const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "test123";
            await helpers.login(username, password);
            await page.waitForTimeout(2000);
        }

        // Rapid navigation test
        const modules = ["patient", "queue", "inventory", "settings"];

        for (let i = 0; i < 3; i++) {
            for (const module of modules) {
                const isAvailable = await helpers.checkModuleAvailable(module);
                if (isAvailable) {
                    await helpers.navigateToModule(module);
                    await page.waitForTimeout(100); // Minimal wait
                }
            }
        }

        // App should still be responsive
        await expect(page.locator("#root")).toBeVisible();
    });

    test("should handle memory usage efficiently", async ({ page }) => {
        await page.goto("/");
        await helpers.waitForAppLoad();

        // Get initial memory usage
        const initialMemory = await page.evaluate(() => {
            return (performance as any).memory?.usedJSHeapSize || 0;
        });

        // Perform various operations
        for (let i = 0; i < 10; i++) {
            await page.reload();
            await helpers.waitForAppLoad();
            await page.waitForTimeout(500);
        }

        // Check final memory usage
        const finalMemory = await page.evaluate(() => {
            return (performance as any).memory?.usedJSHeapSize || 0;
        });

        // Memory shouldn't grow excessively (this is a rough check)
        if (initialMemory > 0 && finalMemory > 0) {
            const memoryGrowth = finalMemory - initialMemory;
            const growthRatio = memoryGrowth / initialMemory;

            console.log(
                `Memory growth: ${memoryGrowth} bytes (${(growthRatio * 100).toFixed(2)}%)`
            );

            // Memory growth should be reasonable (less than 500% increase)
            expect(growthRatio).toBeLessThan(5);
        }
    });
});

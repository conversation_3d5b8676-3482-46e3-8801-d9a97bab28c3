# Homebase Playwright Test Suite

This directory contains a comprehensive Playwright-based test suite for the apps/homebase React application. The test suite is designed to validate the modern rewrite of the legacy jsshared, jshomebase, and tshomebase codebases.

## Overview

The test suite covers:
- **NES Backend Connectivity**: Validates connection to the NES backend API
- **Authentication Flow**: Tests login functionality with environment credentials
- **DSL Form Loading**: Validates Domain Specific Language form loading and processing
- **Navigation**: Tests application navigation and module functionality
- **Integration**: End-to-end user workflows and state management
- **Performance**: Load times, memory usage, and reliability testing

## Test Structure

```
__tests__/
├── setup.ts              # Global test setup and environment validation
├── connectivity.test.ts  # NES backend connectivity tests
├── login.test.ts         # Authentication and login flow tests
├── navigation.test.ts    # UI navigation and component tests
├── dsl.test.ts          # DSL form loading and processing tests
├── integration.test.ts   # End-to-end integration tests
└── README.md            # This file

__mocks__/
├── api-responses.ts     # Mock API responses for testing
└── test-helpers.ts      # Utility functions for test scenarios
```

## Environment Configuration

The test suite uses environment variables for configuration:

- `PLAYWRIGHT_TEST_USERNAME`: Username for authentication tests (default: 'csr')
- `PLAYWRIGHT_TEST_PASSWORD`: Password for authentication tests (default: 'test123')
- `BASE_URL`: Base URL for the application (default: 'https://dev.local.clararx.com')
- `NODE_ENV`: Should be set to 'testing'

These variables are automatically loaded from `configs/.env.[username]` files using dotenvx.

## Running Tests

### Prerequisites

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Install Playwright browsers:
   ```bash
   pnpm exec playwright install
   ```

### Test Commands

```bash
# Run all tests
pnpm test

# Run tests with UI mode
pnpm test:ui

# Run tests in debug mode
pnpm test:debug

# Run specific test file
pnpm exec playwright test connectivity.test.ts

# Run tests in specific browser
pnpm exec playwright test --project=chromium
```

## Test Scenarios

### 1. Connectivity Tests (`connectivity.test.ts`)
- NES backend health endpoint validation
- DSL endpoint availability
- Authentication endpoint validation
- Network error handling
- SSL/TLS configuration validation

### 2. Login Tests (`login.test.ts`)
- Login page loading
- Form validation
- Invalid credential handling
- Successful authentication flow
- Session persistence
- Security validation

### 3. Navigation Tests (`navigation.test.ts`)
- Main application interface loading
- Navigation menu functionality
- Module navigation (patient, queue, inventory, etc.)
- Responsive design validation
- Browser navigation (back/forward)
- Mantine UI component usage

### 4. DSL Tests (`dsl.test.ts`)
- DSL form loading from NES backend
- Patient, assessment, and order form availability
- DSL form processing and field operations
- Model sections and relationships
- Error handling for DSL failures

### 5. Integration Tests (`integration.test.ts`)
- Complete user workflows
- Real vs. mocked backend testing
- State persistence across refreshes
- Network interruption handling
- Concurrent user sessions
- Performance and memory usage

## Mock System

The test suite includes a comprehensive mock system for the NES backend:

### API Response Mocks (`__mocks__/api-responses.ts`)
- Authentication responses (success/failure)
- DSL form definitions (patient, assessment, order)
- User data and permissions
- Module configurations
- Error responses for various scenarios

### Test Helpers (`__mocks__/test-helpers.ts`)
- `TestHelpers` class with utility methods:
  - `mockNESBackend()`: Configure backend mocking
  - `waitForAppLoad()`: Wait for React app initialization
  - `checkNESConnectivity()`: Validate real backend availability
  - `login()`: Perform authentication flow
  - `isLoggedIn()`: Check authentication state
  - `waitForDSLLoad()`: Wait for DSL forms to load
  - `navigateToModule()`: Navigate to specific modules

## Configuration

### Playwright Configuration (`playwright.config.ts`)
- TypeScript support with path mapping
- Environment variable loading via dotenvx
- Multiple browser testing (Chrome, Firefox, Safari, Edge)
- Mobile viewport testing
- Local development server integration
- Trace collection and screenshot capture

### Global Setup (`setup.ts`)
- Environment validation
- Connectivity checks
- Build validation
- Test environment preparation

## Legacy Code Integration

The test suite is designed based on analysis of the legacy codebases:

### From jsshared/jshomebase (CoffeeScript/jQuery/Backbone):
- Authentication patterns
- Module structure (patient, queue, inventory, etc.)
- Form handling approaches
- Navigation patterns

### From tshomebase (TypeScript/React):
- DSL form processing
- Component registry patterns
- Module definitions
- State management approaches

## Best Practices

1. **Environment Isolation**: Tests use mocked backends by default but can test against real backends when available
2. **Graceful Degradation**: Tests handle missing features or unavailable services gracefully
3. **Performance Awareness**: Tests include performance checks and memory usage validation
4. **Security Testing**: Authentication and session management are thoroughly tested
5. **Cross-Browser Compatibility**: Tests run across multiple browsers and viewports
6. **Maintainability**: Modular test structure with reusable helpers and mocks

## Troubleshooting

### Common Issues

1. **Environment Variables Not Loading**
   - Ensure `configs/.env.[username]` file exists
   - Check dotenvx installation and configuration
   - Verify file permissions

2. **NES Backend Connectivity**
   - Check BASE_URL configuration
   - Verify network connectivity
   - Review SSL/TLS certificate issues

3. **Test Timeouts**
   - Increase timeout values in playwright.config.ts
   - Check for slow network conditions
   - Verify application build performance

4. **Authentication Failures**
   - Verify PLAYWRIGHT_TEST_USERNAME and PLAYWRIGHT_TEST_PASSWORD
   - Check credential validity with real backend
   - Review authentication endpoint configuration

### Debug Mode

Use debug mode for detailed test execution:
```bash
pnpm test:debug
```

This will:
- Open browser in headed mode
- Enable step-by-step debugging
- Show detailed console output
- Allow manual interaction during test execution

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use the TestHelpers class for common operations
3. Include both mocked and real backend scenarios where applicable
4. Add appropriate error handling and graceful degradation
5. Update this README with new test scenarios
6. Ensure tests are cross-browser compatible

## Future Enhancements

Planned improvements:
- Visual regression testing
- Accessibility testing (WCAG compliance)
- API contract testing
- Load testing scenarios
- Automated screenshot comparison
- CI/CD integration optimizations
